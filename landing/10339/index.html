<!DOCTYPE html>
<html lang="zh">

<head>
    <meta charset="UTF-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>知崖</title>
    <script crossorigin src="../../js/landing.js" type="module"></script>
    <style>
        body {
            font-family: "Helvetica Neue", Helvetica, Arial, sans-serif;
            background-color: #fff;
            margin: 0;
            padding: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            display: flex;
            flex-direction: column;
            width: 100%;
            max-width: 500px;
            min-height: 100vh;
        }

        .header {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .header-item {
            margin: 5px;
            font-size: 13px;
            color: #555;
        }

        .bg {
            width: 100%;
            flex-grow: 1;
        }
    </style>
</head>

<body>
<div class="container">
    <div class="header">
        <div class="header-item">
            <span style="margin-right: 10px">快应用名称: 知崖</span>
            <span>快应用版本: 1.0.0</span>
        </div>
        <div class="header-item">开发者: 上海间月网络科技有限公司</div>
        <div class="header-item">
            <a href="https://landing.serveclouds.com/privacy/10328/PrivacyPolicy.html"  style="margin-right: 20px">隐私政策&gt;&gt;</a>
            <a href="https://beian.miit.gov.cn/">备案号：沪ICP备2023003471号-3</a>
        </div>
    </div>
    <img class="bg" src="./bg.webp" />
</div>
<script>
  const downloadButton = document.querySelector('.bg');
  downloadButton.addEventListener('click', function (event) {
    window.location.href = "hap://app/com.xiaoje.quantumflux/pages/Flash";
  });
</script>
</body>

</html>